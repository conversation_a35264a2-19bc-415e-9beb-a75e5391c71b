import { useQuery } from "@tanstack/react-query";
import { Repository } from "@/src/types/repository";

export function useRepositories() {
    return useQuery<Repository[]>({
        queryKey: ['repositories'],
        queryFn: async () => {
            const response = await fetch("/api/repositories")
            if (!response.ok) {
                throw new Error(`Failed to fetch repositories: ${response.statusText}`)
            }
            const data = await response.json()

            return data.repositories
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        refetchOnWindowFocus: true
    })
}