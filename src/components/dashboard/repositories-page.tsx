"use client";

import React, { useState } from "react";
import { 
  Search,
  Plus,
  ChevronUp,
  ChevronDown,
  Settings
} from "lucide-react";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { useRepositories } from "@/src/hooks/use-repositories";

// Repository List Component
export function RepositoryList() {
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const { data: repositories, isLoading, error } = useRepositories()

  const mockRepositories = [
    { name: "platyfend-app" },
  ];

  const filteredRepos = mockRepositories.filter((repo) =>
    repo.name.toLowerCase().includes(searchQuery.toLowerCase())
  ).sort((a, b) => {
    const comparison = a.name.localeCompare(b.name);
    return sortOrder === 'asc' ? comparison : -comparison;
  });

  // Pagination calculations
  const rowsPerPageNum = parseInt(rowsPerPage);
  const totalPages = Math.ceil(filteredRepos.length / rowsPerPageNum);
  const startIndex = (currentPage - 1) * rowsPerPageNum;
  const endIndex = startIndex + rowsPerPageNum;
  const paginatedRepos = filteredRepos.slice(startIndex, endIndex);

  // Pagination handlers
  const goToFirstPage = () => setCurrentPage(1);
  const goToPreviousPage = () => setCurrentPage(Math.max(1, currentPage - 1));
  const goToNextPage = () => setCurrentPage(Math.min(totalPages, currentPage + 1));
  const goToLastPage = () => setCurrentPage(totalPages);

  // Reset to first page when search query or rows per page changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, rowsPerPage]);

  const handleSort = () => {
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-xl sm:text-2xl font-semibold text-slate-900">Repositories</h1>
          <p className="text-slate-600 mt-1 text-sm sm:text-base">
            List of repositories accessible to Platyfend.
          </p>
        </div>
        <Button className="bg-[#00617b] hover:bg-[#004a5c] text-white shadow-sm w-full sm:w-auto cursor-pointer">
          <Plus className="w-4 h-4 mr-2" />
          Add Repositories
        </Button>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-3 w-4 h-4 text-slate-400" />
        <Input
          placeholder="Repo not found? Search here..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 w-full sm:max-w-md rounded-lg shadow-sm border-slate-300 focus:border-[#00617b] focus:ring-[#00617b]"
        />
      </div>

      {/* Repository Table */}
      <div className="bg-white rounded-lg border border-slate-200 shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[400px]">
            <thead className="bg-slate-50 border-b border-slate-200">
              <tr>
                <th className="text-left py-3 sm:py-4 px-4 sm:px-6 font-medium text-slate-700">
                  <button
                    onClick={handleSort}
                    className="flex items-center gap-1 hover:text-slate-900 transition-colors text-sm sm:text-base cursor-pointer"
                  >
                    Repository
                    {sortOrder === "asc" ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedRepos.map((repo) => (
                <tr
                  key={repo.name}
                  className="border-b border-slate-200 hover:bg-slate-50 group relative transition-colors"
                >
                  <td className="py-3 sm:py-4 px-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-slate-900 text-sm sm:text-base">{repo.name}</div>
                      <button className="sm:opacity-0 sm:group-hover:opacity-100 transition-opacity p-1 hover:bg-slate-200 rounded flex-shrink-0 cursor-pointer">
                        <Settings className="w-4 h-4 text-slate-500" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between px-4 sm:px-6 py-4 border-t border-slate-200 bg-slate-50 space-y-3 sm:space-y-0">
          <div className="flex items-center gap-2 text-sm text-slate-600">
            <span className="hidden sm:inline">Rows per page</span>
            <span className="sm:hidden">Per page:</span>
            <Select value={rowsPerPage} onValueChange={setRowsPerPage}>
              <SelectTrigger className="w-16 sm:w-20 h-10 rounded-md shadow-sm border-slate-300">
                <SelectValue className="text-slate-600" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5" className="text-slate-600 bg-white rounded-md shadow-sm my-1">
                  5
                </SelectItem>

                <SelectItem value="10" className="text-slate-600 bg-white rounded-md shadow-sm my-1">
                  10
                </SelectItem>

                <SelectItem value="20" className="text-slate-600 bg-white rounded-md shadow-sm my-1">
                  20
                </SelectItem>

                <SelectItem value="50" className="text-slate-600 bg-white rounded-md shadow-sm my-1">
                  50
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-center sm:justify-end gap-4">
            <span className="text-sm text-slate-600">
              Page {currentPage} of {totalPages || 1}
            </span>
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={goToFirstPage}
                className="h-8 w-8 p-0 text-slate-600 shadow-sm rounded-md cursor-pointer"
              >
                ≪
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={goToPreviousPage}
                className="h-8 w-8 p-0 text-slate-600 shadow-sm rounded-md cursor-pointer"
              >
                ‹
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages || totalPages === 0}
                onClick={goToNextPage}
                className="h-8 w-8 p-0 text-slate-600 shadow-sm rounded-md cursor-pointer"
              >
                ›
              </Button>
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages || totalPages === 0}
                onClick={goToLastPage}
                className="h-8 w-8 p-0 text-slate-600 shadow-sm rounded-md cursor-pointer"
              >
                ≫
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Export a page component that uses the repository list
export function RepositoriesPage() {
  return <RepositoryList />;
}
