import { NextRequest } from 'next/server'
import { authOptions } from '@/src/features/auth/lib/auth-config'
import { getServerSession } from 'next-auth'
import { NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
    try {
        // Get authenticated session
        const session = await getServerSession(authOptions)
        console.log(session)
        
        // Validate authentication
        if (!session) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
        }
        
        // Mock repository data until implementation is complete
        const mockRepositories = [
            {
                id: "1",
                name: "platyfend-app",
                fullName: "user/platyfend-app",
                description: "Main Platyfend application",
                url: "https://github.com/user/platyfend-app",
                isPrivate: true,
                language: "TypeScript",
                stars: 12,
                forks: 3,
                externalId: "ext-1",
                vcsInstallationId: "install-1",
                workspaceId: "workspace-1",
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                id: "2",
                name: "platyfend-api",
                fullName: "user/platyfend-api",
                description: "Platyfend API service",
                url: "https://github.com/user/platyfend-api",
                isPrivate: true,
                language: "Node.js",
                stars: 8,
                forks: 1,
                externalId: "ext-2",
                vcsInstallationId: "install-1",
                workspaceId: "workspace-1",
                createdAt: new Date(),
                updatedAt: new Date()
            }
        ]

        return NextResponse.json({ repositories: mockRepositories })
        
    } catch (error) {
        console.error('Error in repositories API:', error)
        return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
    }
}